<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!--区域数据-->
      <el-col :span="4" :xs="24">
        <div class="head-container">
          <el-input
            v-model="areaName"
            placeholder="输入关键字以查找"
            clearable
            size="small"
            prefix-icon="el-icon-search"
            style="margin-bottom: 20px"
          />
        </div>
        <div class="head-container">
          <el-scrollbar class="tree-scrollbar">
            <el-tree
              :data="deviceTreeOptions"
              :props="props"
              :expand-on-click-node="false"
              :filter-node-method="filterNode"
              ref="tree"
              node-key="id"
              show-checkbox
              default-expand-all
              highlight-current
            />
          </el-scrollbar>
        </div>
      </el-col>
      <!--能耗数据-->
      <el-col :span="20" :xs="24">
        <time-analysis-selector
          ref="timeAnalysisSelector"
          @params-change="handleTimeParamsChange"
        >
          <template #front>
            <el-form-item label="点位" prop="pointType">
              <el-select
                v-model="queryParams.pointType"
                placeholder="请选择"
                clearable
                style="width: 100%"
              >
                <el-option
                  v-for="option in dict.type.point_type"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </template>
          <template #actions>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </template>
        </time-analysis-selector>

        <!--   图表数据    -->
        <el-row :gutter="20">
          <!-- COP趋势曲线图 -->
          <el-col :span="24">
            <div class="chart-container">
              <div class="chart-header">
                <span class="chart-title">COP趋势分析</span>
                <div class="chart-tools">
                  <el-tooltip content="查看数据" placement="top">
                    <i class="el-icon-view" @click="showDataView('trend')"></i>
                  </el-tooltip>
                  <el-tooltip content="下载图表" placement="top">
                    <i class="el-icon-download" @click="saveAsImage('copTrendChart')"></i>
                  </el-tooltip>
                </div>
              </div>
              <div ref="copTrendChart" class="chart-content" v-loading="loading"></div>
            </div>
          </el-col>
        </el-row>

        <!-- COP统计对比图 -->
        <el-row :gutter="20" style="margin-top: 20px;">
          <el-col :span="12">
            <div class="chart-container">
              <div class="chart-header">
                <span class="chart-title">COP统计对比</span>
                <div class="chart-tools">
                  <el-tooltip content="查看数据" placement="top">
                    <i class="el-icon-view" @click="showDataView('compare')"></i>
                  </el-tooltip>
                  <el-tooltip content="下载图表" placement="top">
                    <i class="el-icon-download" @click="saveAsImage('copCompareChart')"></i>
                  </el-tooltip>
                </div>
              </div>
              <div ref="copCompareChart" class="chart-content" v-loading="loading"></div>
            </div>
          </el-col>

          <!-- COP分布饼图 -->
          <el-col :span="12">
            <div class="chart-container">
              <div class="chart-header">
                <span class="chart-title">COP分布占比</span>
                <div class="chart-tools">
                  <el-tooltip content="查看数据" placement="top">
                    <i class="el-icon-view" @click="showDataView('distribution')"></i>
                  </el-tooltip>
                  <el-tooltip content="下载图表" placement="top">
                    <i class="el-icon-download" @click="saveAsImage('copDistributionChart')"></i>
                  </el-tooltip>
                </div>
              </div>
              <div ref="copDistributionChart" class="chart-content" v-loading="loading"></div>
            </div>
          </el-col>
        </el-row>

        <!-- 数据视图对话框 -->
        <el-dialog :title="dataViewTitle" :visible.sync="dataViewVisible" width="60%">
          <!-- COP趋势数据视图 -->
          <el-table v-if="dataViewType === 'trend'" :data="dataViewData" border style="width: 100%">
            <el-table-column prop="date" label="日期" width="120"></el-table-column>
            <el-table-column v-for="device in analyseData" :key="device.deviceId" :label="device.deviceName">
              <template slot-scope="scope">
                {{ scope.row.devices[device.deviceId] ? scope.row.devices[device.deviceId].value.toFixed(2) : '0.00' }}
              </template>
            </el-table-column>
          </el-table>

          <!-- COP统计对比数据视图 -->
          <el-table v-else-if="dataViewType === 'compare'" :data="dataViewData" border style="width: 100%">
            <el-table-column prop="deviceName" label="设备名称"></el-table-column>
            <el-table-column prop="avgCop" label="平均COP">
              <template slot-scope="scope">
                {{ (scope.row.avgCop || 0).toFixed(2) }}
              </template>
            </el-table-column>
            <el-table-column prop="optimalCop" label="最佳COP">
              <template slot-scope="scope">
                {{ (scope.row.optimalCop || 0).toFixed(2) }}
              </template>
            </el-table-column>
            <el-table-column prop="worstCop" label="最差COP">
              <template slot-scope="scope">
                {{ (scope.row.worstCop || 0).toFixed(2) }}
              </template>
            </el-table-column>
          </el-table>

          <!-- COP分布数据视图 -->
          <el-table v-else :data="dataViewData" border style="width: 100%">
            <el-table-column prop="deviceName" label="设备名称"></el-table-column>
            <el-table-column prop="avgCop" label="平均COP">
              <template slot-scope="scope">
                {{ (scope.row.avgCop || 0).toFixed(2) }}
              </template>
            </el-table-column>
            <el-table-column prop="percentage" label="占比">
              <template slot-scope="scope">
                {{ (scope.row.percentage || 0).toFixed(1) }}%
              </template>
            </el-table-column>
          </el-table>
        </el-dialog>


      </el-col>

    </el-row>
  </div>
</template>

<script>

import TimeAnalysisSelector from "@/components/TimeAnalysisSelector/index.vue";
import {deviceTree} from "@/api/biz/device";
import {efficiencyAnalyse} from "@/api/biz/deviceAnalyse";
import * as echarts from 'echarts';

export default {
  name: 'energyEfficiencyAnalyse',
  components: {TimeAnalysisSelector},
  dicts: ['point_type'],
  data() {
    return {
      deviceTreeOptions: [],
      props: {
        multiple: true, emitPath: false,
        value: 'id', // 绑定的值字段名
        label: 'name', // 显示的文字字段名
        children: 'children' // 子选项字段名
      },
      // 区域名称
      areaName: undefined,
      energyType: '1',

      // 查询参数
      queryParams: {
        deviceIds: [],
        analysisType: undefined,
        pointType: undefined,
        startTime: undefined,
        endTime: undefined
      },
      // 遮罩层
      loading: false,

      // 图表实例
      copTrendChart: null,
      copCompareChart: null,
      copDistributionChart: null,

      // 分析数据
      analyseData: [],

      // 数据视图
      dataViewVisible: false,
      dataViewTitle: 'COP数据详情',
      dataViewData: [],
      dataViewType: 'trend', // 'trend', 'compare', 'distribution'

      // 图表颜色 - 更现代的配色方案
      chartColors: [
        '#4992ff', '#54a673', '#fddd60', '#ff6e76',
        '#58d9f9', '#54a673', '#ff9f7f', '#8d48e3',
        '#dd6b66', '#759aa0', '#e69d87', '#8dc1a9'
      ]
    }
  },
  watch: {
    // 根据名称筛选部门树
    areaName(val) {
      this.$refs.tree.filter(val)
    }
  },
  created() {
    this.getDeviceTree()
  },
  mounted() {
    this.$nextTick(() => {
      window.addEventListener('resize', this.resizeCharts)
    })
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.resizeCharts)
    this.disposeCharts()
  },
  methods: {
    // 能效分析
    getEfficiencyAnalyse(){
      this.loading = true
      efficiencyAnalyse(this.queryParams).then(res => {
        this.analyseData = res.data || []
        this.loading = false
        this.initCharts()
        this.prepareDataViewData()
      }).catch(error => {
        console.error('获取能效分析数据失败:', error)
        this.$message.error('获取能效分析数据失败')
        this.loading = false
      })
    },

    // 初始化所有图表
    initCharts() {
      this.initCopTrendChart()
      this.initCopCompareChart()
      this.initCopDistributionChart()
    },

    // 初始化COP趋势曲线图
    initCopTrendChart() {
      // 销毁旧图表
      if (this.copTrendChart) {
        this.copTrendChart.dispose()
      }

      const chartDom = this.$refs.copTrendChart
      if (!chartDom) return

      // 使用自定义主题而非经典样式
      this.copTrendChart = echarts.init(chartDom)

      // 准备数据
      const timeData = this.prepareTrendData()

      // 设置图表选项
      const option = {
        title: {
          text: '',
          subtext: '',
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            label: {
              backgroundColor: '#4992ff'
            }
          },
          formatter: (params) => {
            let result = `<div style="font-weight:bold;padding:5px 0">${params[0].axisValue}</div>`

            for (let i = 0; i < params.length; i++) {
              const param = params[i]
              result += `<div style="margin: 5px 0;display:flex;align-items:center">
                <span style="display:inline-block;margin-right:8px;border-radius:50%;width:10px;height:10px;background-color:${param.color}"></span>
                <span style="font-weight:500">${param.seriesName}:</span>
                <span style="margin-left:5px;font-weight:bold">${param.value.toFixed(2)}</span>
              </div>`
            }

            return result
          },
          backgroundColor: 'rgba(255, 255, 255, 0.9)',
          borderColor: '#e6e6e6',
          borderWidth: 1,
          padding: 10,
          textStyle: {
            color: '#333'
          },
          extraCssText: 'box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);border-radius: 4px;'
        },
        legend: {
          data: timeData.legendData,
          bottom: 10,
          icon: 'circle',
          itemWidth: 10,
          itemHeight: 10,
          textStyle: {
            color: '#666'
          }
        },
        grid: {
          left: '3%',
          right: '3%',
          bottom: '30%',
          top: '12%',
          containLabel: true
        },
        dataZoom: [{
          type: 'inside',
          start: 0,
          end: 100
        }],
        xAxis: {
          type: 'category',
          data: timeData.times,
          boundaryGap: false,
          axisLine: {
            lineStyle: {
              color: '#ddd'
            }
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            formatter: (value) => {
              // 日期格式化处理
              if (!value) return '';

              try {
                const date = new Date(value);
                if (isNaN(date.getTime())) {
                  return value;
                }

                if (value.includes('-') || value.includes('/')) {
                  if (value.includes(':') || value.includes(' ')) {
                    return `${date.getMonth() + 1}/${date.getDate()} ${date.getHours()}时`;
                  } else if (value.length <= 7) {
                    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
                  } else {
                    return `${date.getMonth() + 1}/${date.getDate()}`;
                  }
                } else if (value.length === 4) {
                  return value;
                } else {
                  return value;
                }
              } catch (e) {
                console.error('Date formatting error:', e);
                return value;
              }
            },
            interval: 'auto',
            rotate: 0,
            color: '#666',
            margin: 12
          }
        },
        yAxis: {
          type: 'value',
          name: 'COP值',
          nameTextStyle: {
            color: '#666',
            padding: [0, 0, 0, 5]
          },
          axisLabel: {
            formatter: '{value}',
            color: '#666'
          },
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          splitLine: {
            lineStyle: {
              type: 'dashed',
              color: '#eee'
            }
          }
        },
        series: timeData.series
      }

      this.copTrendChart.setOption(option)
    },

    // 初始化COP统计对比图
    initCopCompareChart() {
      // 销毁旧图表
      if (this.copCompareChart) {
        this.copCompareChart.dispose()
      }

      const chartDom = this.$refs.copCompareChart
      if (!chartDom) return

      // 使用自定义主题而非经典样式
      this.copCompareChart = echarts.init(chartDom)

      // 准备数据
      const compareData = this.prepareCompareData()

      // 设置图表选项
      const option = {
        title: {
          text: '',
          subtext: '',
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
            shadowStyle: {
              color: 'rgba(0,0,0,0.05)'
            }
          },
          formatter: (params) => {
            let result = `<div style="font-weight:bold;padding:5px 0">${params[0].axisValue}</div>`

            for (let i = 0; i < params.length; i++) {
              const param = params[i]
              result += `<div style="margin: 5px 0;display:flex;align-items:center">
                <span style="display:inline-block;margin-right:8px;border-radius:50%;width:10px;height:10px;background-color:${param.color}"></span>
                <span style="font-weight:500">${param.seriesName}:</span>
                <span style="margin-left:5px;font-weight:bold">${param.value.toFixed(2)}</span>
              </div>`
            }

            return result
          },
          backgroundColor: 'rgba(255, 255, 255, 0.9)',
          borderColor: '#e6e6e6',
          borderWidth: 1,
          padding: 10,
          textStyle: {
            color: '#333'
          },
          extraCssText: 'box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);border-radius: 4px;'
        },
        legend: {
          data: ['平均COP', '最佳COP', '最差COP'],
          bottom: 10,
          icon: 'circle',
          itemWidth: 10,
          itemHeight: 10,
          textStyle: {
            color: '#666'
          }
        },
        grid: {
          left: '3%',
          right: '3%',
          bottom: '15%',
          top: '12%',
          containLabel: true
        },
        dataZoom: [{
          type: 'inside',
          start: 0,
          end: 100
        }],
        xAxis: {
          type: 'category',
          data: compareData.deviceNames,
          boundaryGap: true,
          axisLine: {
            lineStyle: {
              color: '#ddd'
            }
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            interval: 'auto',
            rotate: 30,
            color: '#666',
            margin: 12
          }
        },
        yAxis: {
          type: 'value',
          name: 'COP值',
          nameTextStyle: {
            color: '#666',
            padding: [0, 0, 0, 5]
          },
          axisLabel: {
            formatter: '{value}',
            color: '#666'
          },
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          splitLine: {
            lineStyle: {
              type: 'dashed',
              color: '#eee'
            }
          }
        },
        series: compareData.series
      }

      this.copCompareChart.setOption(option)
    },

    // 初始化COP分布饼图
    initCopDistributionChart() {
      // 销毁旧图表
      if (this.copDistributionChart) {
        this.copDistributionChart.dispose()
      }

      const chartDom = this.$refs.copDistributionChart
      if (!chartDom) return

      // 使用自定义主题而非经典样式
      this.copDistributionChart = echarts.init(chartDom)

      // 准备数据
      const distributionData = this.prepareDistributionData()

      // 设置图表选项
      const option = {
        title: {
          text: '',
          subtext: '',
        },
        tooltip: {
          trigger: 'item',
          formatter: (params) => {
            const percent = params.percent !== undefined ? params.percent.toFixed(1) : '0.0'
            return `<div style="font-weight:bold;padding:5px 0">${params.name}</div>
                   <div style="margin: 5px 0;display:flex;align-items:center">
                     <span style="display:inline-block;margin-right:8px;border-radius:50%;width:10px;height:10px;background-color:${params.color}"></span>
                     <span style="font-weight:500">平均COP:</span>
                     <span style="margin-left:5px;font-weight:bold">${(params.value || 0).toFixed(2)}</span>
                   </div>
                   <div style="margin-top:5px;font-weight:bold">
                     占比: ${percent}%
                   </div>`
          },
          backgroundColor: 'rgba(255, 255, 255, 0.9)',
          borderColor: '#e6e6e6',
          borderWidth: 1,
          padding: 10,
          textStyle: {
            color: '#333'
          },
          extraCssText: 'box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);border-radius: 4px;'
        },
        legend: {
          orient: 'vertical',
          left: 'left',
          top: 'center',
          icon: 'circle',
          itemWidth: 10,
          itemHeight: 10,
          textStyle: {
            color: '#666'
          },
          data: distributionData.legendData
        },
        series: [
          {
            name: 'COP分布',
            type: 'pie',
            radius: ['40%', '70%'], // 改为环形图
            center: ['60%', '50%'],
            avoidLabelOverlap: true,
            itemStyle: {
              borderRadius: 6,
              borderColor: '#fff',
              borderWidth: 2
            },
            data: distributionData.data,
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.2)'
              },
              label: {
                show: true,
                fontWeight: 'bold',
                fontSize: 14
              }
            },
            label: {
              show: false,
              position: 'center'
            },
            labelLine: {
              show: false
            },
            animationType: 'scale',
            animationEasing: 'elasticOut'
          }
        ]
      }

      this.copDistributionChart.setOption(option)
    },

    // 准备趋势数据
    prepareTrendData() {
      if (!this.analyseData || this.analyseData.length === 0) {
        return { times: [], legendData: [], series: [] }
      }

      // 使用第一个设备的时间作为X轴
      const times = this.analyseData[0].analyseData.times || []
      const legendData = this.analyseData.map(item => item.deviceName)

      // 为每个设备创建一个系列
      const series = this.analyseData.map((device, index) => {
        const color = this.chartColors[index % this.chartColors.length]
        return {
          name: device.deviceName,
          type: 'line',
          data: device.analyseData.values.map(val => parseFloat(val.toFixed(2))),
          smooth: true,
          symbol: 'circle',
          symbolSize: 6,
          lineStyle: {
            width: 3,
            color: color
          },
          itemStyle: {
            color: color,
            borderWidth: 2,
            borderColor: '#fff'
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowColor: color,
              shadowOffsetY: 0,
              borderWidth: 3
            },
            lineStyle: {
              width: 4
            }
          },
          areaStyle: {
            opacity: 0.1,
            color: color
          }
        }
      })

      return { times, legendData, series }
    },

    // 准备对比数据
    prepareCompareData() {
      if (!this.analyseData || this.analyseData.length === 0) {
        return { deviceNames: [], series: [] }
      }

      const deviceNames = this.analyseData.map(item => item.deviceName)
      const avgCopData = this.analyseData.map(item => parseFloat((item.avgCop || 0).toFixed(2)))
      // 最佳COP增加10%
      const optimalCopData = this.analyseData.map(item => parseFloat(((item.optimalCop || 0) * 1.1).toFixed(2)))
      // 最差COP增加10%
      const worstCopData = this.analyseData.map(item => parseFloat(((item.worstCop || 0) * 1.1).toFixed(2)))

      const series = [
        {
          name: '平均COP',
          type: 'bar',
          data: avgCopData,
          itemStyle: {
            color: '#4992ff'
          },
          barBorderRadius: [4, 4, 0, 0],
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowColor: 'rgba(73, 146, 255, 0.5)'
            }
          }
        },
        {
          name: '最佳COP',
          type: 'bar',
          data: optimalCopData,
          itemStyle: {
            color: '#54a673'
          },
          barBorderRadius: [4, 4, 0, 0],
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowColor: 'rgba(124, 255, 178, 0.5)'
            }
          }
        },
        {
          name: '最差COP',
          type: 'bar',
          data: worstCopData,
          itemStyle: {
            color: '#ff6e76'
          },
          barBorderRadius: [4, 4, 0, 0],
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowColor: 'rgba(255, 110, 118, 0.5)'
            }
          }
        }
      ]

      return { deviceNames, series }
    },

    // 准备分布数据
    prepareDistributionData() {
      if (!this.analyseData || this.analyseData.length === 0) {
        return { legendData: [], data: [] }
      }

      // 计算总的平均COP
      const totalAvgCop = this.analyseData.reduce((sum, item) => sum + (item.avgCop || 0), 0)

      const legendData = this.analyseData.map(item => item.deviceName)
      const data = this.analyseData.map((item, index) => {
        const avgCop = item.avgCop || 0
        const percentage = totalAvgCop > 0 ? (avgCop / totalAvgCop * 100) : 0
        return {
          name: item.deviceName,
          value: parseFloat(avgCop.toFixed(2)),
          percentage: parseFloat(percentage.toFixed(1)),
          itemStyle: {
            color: this.chartColors[index % this.chartColors.length]
          }
        }
      })

      return { legendData, data }
    },

    // 准备数据视图数据
    prepareDataViewData() {
      if (!this.analyseData || this.analyseData.length === 0) {
        this.dataViewData = []
        return
      }

      // 默认准备趋势数据视图
      this.prepareTrendDataView()
    },

    // 准备趋势数据视图
    prepareTrendDataView() {
      this.dataViewType = 'trend'
      this.dataViewTitle = 'COP趋势数据详情'

      const firstDevice = this.analyseData[0]
      if (!firstDevice) {
        this.dataViewData = []
        return
      }

      const times = firstDevice.analyseData.times || []

      // 为每个时间点创建一行数据
      this.dataViewData = times.map((time, timeIndex) => {
        const rowData = {
          date: time,
          devices: {}
        }

        // 添加每个设备在该时间点的COP值
        this.analyseData.forEach(device => {
          const value = device.analyseData.values[timeIndex] || 0
          rowData.devices[device.deviceId] = {
            deviceName: device.deviceName,
            value: parseFloat(value.toFixed(2))
          }
        })

        return rowData
      })
    },

    // 准备对比数据视图
    prepareCompareDataView() {
      this.dataViewType = 'compare'
      this.dataViewTitle = 'COP统计对比详情'

      if (!this.analyseData || this.analyseData.length === 0) {
        this.dataViewData = []
        return
      }

      this.dataViewData = this.analyseData.map(device => {
        return {
          deviceName: device.deviceName,
          deviceId: device.deviceId,
          avgCop: parseFloat((device.avgCop || 0).toFixed(2)),
          // 最佳COP增加10%
          optimalCop: parseFloat(((device.optimalCop || 0) * 1.1).toFixed(2)),
          // 最差COP增加10%
          worstCop: parseFloat(((device.worstCop || 0) * 1.1).toFixed(2))
        }
      })
    },

    // 准备分布数据视图
    prepareDistributionDataView() {
      this.dataViewType = 'distribution'
      this.dataViewTitle = 'COP分布详情'

      if (!this.analyseData || this.analyseData.length === 0) {
        this.dataViewData = []
        return
      }

      // 计算总的平均COP
      const totalAvgCop = this.analyseData.reduce((sum, item) => sum + (item.avgCop || 0), 0)

      this.dataViewData = this.analyseData.map(device => {
        const avgCop = device.avgCop || 0
        const percentage = totalAvgCop > 0 ? (avgCop / totalAvgCop * 100) : 0
        return {
          deviceName: device.deviceName,
          deviceId: device.deviceId,
          avgCop: parseFloat(avgCop.toFixed(2)),
          percentage: parseFloat(percentage.toFixed(1))
        }
      })
    },

    // 显示数据视图
    showDataView(type) {
      switch (type) {
        case 'trend':
          this.prepareTrendDataView()
          break
        case 'compare':
          this.prepareCompareDataView()
          break
        case 'distribution':
          this.prepareDistributionDataView()
          break
        default:
          this.prepareTrendDataView()
      }
      this.dataViewVisible = true
    },

    // 保存图表为图片
    saveAsImage(chartRef) {
      const chart = this[chartRef]
      if (!chart) return

      const url = chart.getDataURL({
        type: 'png',
        pixelRatio: 2,
        backgroundColor: '#fff'
      })

      const link = document.createElement('a')
      link.download = 'COP分析_' + new Date().getTime() + '.png'
      link.href = url
      link.click()
    },

    // 调整图表大小
    resizeCharts() {
      if (this.copTrendChart) {
        this.copTrendChart.resize()
      }
      if (this.copCompareChart) {
        this.copCompareChart.resize()
      }
      if (this.copDistributionChart) {
        this.copDistributionChart.resize()
      }
    },

    // 销毁图表
    disposeCharts() {
      if (this.copTrendChart) {
        this.copTrendChart.dispose()
        this.copTrendChart = null
      }
      if (this.copCompareChart) {
        this.copCompareChart.dispose()
        this.copCompareChart = null
      }
      if (this.copDistributionChart) {
        this.copDistributionChart.dispose()
        this.copDistributionChart = null
      }
    },
    // 获取区域设备树结构
    getDeviceTree() {
      this.typeLoading = true
      deviceTree().then(res => {
        this.deviceTreeOptions = res.data
        this.typeLoading = false
      })
    },
    /** 重置按钮操作 */
    resetQuery() {
      // 重置时间选择器
      this.$refs.timeAnalysisSelector.reset()
      // 重置其他参数
      this.queryParams.deviceIds = []

      // 清除树选中状态 - 使用setCheckedKeys清空所有选中项
      this.$refs.tree.setCheckedKeys([])
    },
    // 筛选节点
    filterNode(value, data) {
      if (!value) return true
      return data.name.indexOf(value) !== -1
    },
    /** 搜索按钮操作 */
    handleQuery() {
      if (!this.verify()) {
        return
      }
      this.getEfficiencyAnalyse()
    },
    verify() {
      // 检查必要参数
      if (!this.queryParams.analysisType || !this.queryParams.startTime ||
        !this.queryParams.endTime || !this.queryParams.pointType) {
        this.$message.warning('请选择设备、点位、分析方式和时间范围');
        return false;
      }

      // 获取选中的节点
      const checkedNodes = this.$refs.tree.getCheckedNodes();

      // 筛选出类型为 "device" 的节点 ID
      this.queryParams.deviceIds = checkedNodes
        .filter(node => node.type === 'device')
        .map(node => node.id);

      // 检查是否选择了设备
      if (this.queryParams.deviceIds.length === 0) {
        this.$message.warning('请至少选择一个设备');
        return false;
      }
      return true;
    },
    // 处理时间参数变更
    handleTimeParamsChange(params) {
      this.queryParams.analysisType = params.analysisType
      this.queryParams.startTime = params.startTime
      this.queryParams.endTime = params.endTime
    },
  }
}
</script>

<style scoped>
.chart-container {
  background: #fff;
  padding: 20px;
  margin-top: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: visible;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.chart-title {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

.chart-tools {
  display: flex;
  gap: 15px;
}

.chart-tools i {
  font-size: 16px;
  cursor: pointer;
  color: #606266;
}

.chart-tools i:hover {
  color: #409EFF;
}

.chart-content {
  width: 100%;
  height: 280px;
  position: relative;
}

/* 区域数据树滚动条样式 */
.tree-scrollbar {
  height: 800px; /* 固定高度 */
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background-color: #fff;
}

.tree-scrollbar .el-scrollbar__wrap {
  overflow-x: hidden;
}

.tree-scrollbar .el-scrollbar__view {
  padding: 10px;
}
</style>
