import request from '@/utils/request'

// 查询设备类型列表
export function listDeviceType(query) {
  return request({
    url: '/biz/deviceType/list',
    method: 'get',
    params: query
  })
}

// 查询设备类型详细
export function getDeviceType(id) {
  return request({
    url: '/biz/deviceType/' + id,
    method: 'get'
  })
}

// 新增设备类型
export function addDeviceType(data) {
  return request({
    url: '/biz/deviceType',
    method: 'post',
    data: data
  })
}

// 修改设备类型
export function updateDeviceType(data) {
  return request({
    url: '/biz/deviceType',
    method: 'put',
    data: data
  })
}

// 删除设备类型
export function delDeviceType(id) {
  return request({
    url: '/biz/deviceType/' + id,
    method: 'delete'
  })
}

// 查询设备类型选项
export function deviceTypeOption(query) {
  return request({
    url: '/biz/deviceType/option',
    method: 'get',
    params: query
  })
}
