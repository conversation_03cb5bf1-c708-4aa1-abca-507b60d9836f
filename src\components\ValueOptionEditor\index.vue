<template>
  <div class="value-option-editor">
    <el-table :data="optionList" border style="width: 100%">
      <el-table-column prop="name" label="名称">
        <template slot-scope="scope">
          <el-input v-model="scope.row.name" placeholder="请输入名称"></el-input>
        </template>
      </el-table-column>
      <el-table-column prop="value" label="值">
        <template slot-scope="scope">
          <el-input v-model="scope.row.value" placeholder="请输入值"></el-input>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="120">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="danger"
            icon="el-icon-delete"
            @click="handleDelete(scope.$index)"
          ></el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="option-actions">
      <el-button type="primary" size="mini" icon="el-icon-plus" @click="handleAdd">添加选项</el-button>
    </div>
  </div>
</template>

<script>
export default {
  name: "ValueOptionEditor",
  props: {
    value: {
      type: String,
      default: '[]'
    }
  },
  data() {
    return {
      optionList: []
    };
  },
  watch: {
    value: {
      immediate: true,
      handler(val) {
        try {
          this.optionList = val ? JSON.parse(val) : [];
        } catch (e) {
          this.optionList = [];
          console.error('JSON解析错误:', e);
        }
      }
    },
    optionList: {
      deep: true,
      handler(val) {
        this.$emit('input', JSON.stringify(val));
      }
    }
  },
  methods: {
    handleAdd() {
      this.optionList.push({
        name: '',
        value: ''
      });
    },
    handleDelete(index) {
      this.optionList.splice(index, 1);
    }
  }
};
</script>

<style scoped>
.value-option-editor {
  margin-bottom: 10px;
}
.option-actions {
  margin-top: 10px;
  text-align: right;
}
</style>