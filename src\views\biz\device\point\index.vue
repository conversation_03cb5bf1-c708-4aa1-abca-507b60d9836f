<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="点位名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入点位名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="地址" prop="address">
        <el-input
          v-model="queryParams.address"
          placeholder="请输入地址"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="绑定设备" prop="deviceName">
        <el-input
          v-model="queryParams.deviceName"
          placeholder="请输入设备名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="操作权限" prop="clientAccess">
        <el-select
          v-model="queryParams.clientAccess"
          clearable
          placeholder="操作权限"
          style="width: 100%"
        >
          <el-option
            v-for="dict in dict.type.client_access"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['biz:devicePoint:add']"
        >新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['biz:devicePoint:edit']"
        >修改
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['biz:devicePoint:remove']"
        >删除
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['biz:devicePoint:export']"
        >导出
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          size="mini"
          @click="autoBoundDevice"
          v-hasPermi="['biz:devicePoint:bound']"
        >绑定设备ID
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="devicePointList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column label="点位名称" align="center" prop="name"/>
      <el-table-column label="字段名称" align="center" prop="fieldName"/>
      <el-table-column label="绑定设备" align="center" prop="deviceName"/>
      <el-table-column label="操作权限" align="center" prop="clientAccess">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.client_access" :value="scope.row.clientAccess"/>
        </template>
      </el-table-column>
      <el-table-column label="数据类型" align="center" prop="dataType"/>
      <el-table-column label="地址" align="center" prop="address"/>
      <el-table-column label="备注" align="center" prop="remark"/>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['biz:devicePoint:edit']"
          >修改
          </el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['biz:devicePoint:remove']"
          >删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改设备点位对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="1000px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="点位名称" prop="name">
              <el-input v-model="form.name" placeholder="请输入点位名称"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="字段名称" prop="fieldName">
              <el-input v-model="form.fieldName" placeholder="请输入名称"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="操作权限" prop="clientAccess">
              <el-select
                v-model="form.clientAccess"
                placeholder="操作权限"
                style="width: 100%"
              >
                <el-option
                  v-for="dict in dict.type.client_access"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="数据类型" prop="dataType">
              <el-select v-model="form.dataType" placeholder="请选择数据类型" style="width: 100%">
                <el-option label="Int" value="Int"/>
                <el-option label="DWord" value="DWord"/>
                <el-option label="Byte" value="Byte"/>
                <el-option label="Float" value="Float"/>
                <el-option label="Word" value="Word"/>
                <el-option label="Boolean" value="Boolean"/>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="是否可分析" prop="analyseFlag">
              <el-select v-model="form.analyseFlag" placeholder="是否统计分析" style="width: 100%">
                <el-option label="是" value="1"/>
                <el-option label="否" value="0"/>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="读数类型" prop="readingType">
              <el-select v-model="form.readingType" placeholder="读数类型" style="width: 100%">
                <el-option label="读数型" value="1"/>
                <el-option label="用量型" value="2"/>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="地址" prop="address">
              <el-input v-model="form.address" placeholder="请输入地址"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="绑定设备" prop="deviceId">
              <el-select v-model="form.deviceId" placeholder="请选择设备" style="width: 100%">
                <el-option
                  v-for="item in deviceOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="控制值类型" prop="valueType">
              <el-select v-model="form.valueType" placeholder="请选择控制值类型" style="width: 100%">
                <el-option label="数值" value="1"/>
                <el-option label="枚举" value="2"/>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="当前值" prop="value">
              <el-input v-model="form.value" :disabled="true" placeholder="当前值"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="form.valueType === '1'">
          <el-col :span="12">
            <el-form-item label="最大值" prop="valueMax">
              <el-input v-model="form.valueMax" placeholder="请输入最大值" type="number"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="最小值" prop="valueMin">
              <el-input v-model="form.valueMin" placeholder="请输入最小值" type="number"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="form.valueType === '2'">
          <el-col :span="24">
            <el-form-item label="枚举选项" prop="valueOption">
              <value-option-editor v-model="form.valueOption"></value-option-editor>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input v-model="form.remark" type="textarea" placeholder="请输入内容"/>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listDevicePoint,
  getDevicePoint,
  delDevicePoint,
  addDevicePoint,
  updateDevicePoint
} from "@/api/biz/devicePoint";
import ValueOptionEditor from "@/components/ValueOptionEditor/index.vue";
import {deviceOption} from "@/api/biz/device";
import {boundDevice} from "../../../../api/biz/devicePoint";

export default {
  name: "DevicePoint",
  components: {
    ValueOptionEditor
  },
  dicts: ['client_access'],
  data() {
    return {
      // 设备选项
      deviceOptions: [],
      // 按钮loading
      buttonLoading: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 设备点位表格数据
      devicePointList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: undefined,
        tagName: undefined,
        deviceId: undefined,
        deviceName: undefined,
        fieldName: undefined,
        analyseFlag: undefined,
        readingType: undefined,
        clientAccess: undefined,
        dataType: undefined,
        address: undefined,
        value: undefined,
        unit: undefined,
        valueType: undefined,
        valueOption: undefined,
        valueMax: undefined,
        valueMin: undefined,
        energyFlag: undefined,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        name: [
          {required: true, message: "点位名称不能为空", trigger: "blur"}
        ],
        tagName: [
          {required: true, message: "标签名称不能为空", trigger: "blur"}
        ],
        deviceId: [
          {required: true, message: "设备不能为空", trigger: "blur"}
        ],
        fieldName: [
          {required: true, message: "字段名称不能为空", trigger: "blur"}
        ],
        analyseFlag: [
          {required: true, message: "分析标识不能为空", trigger: "change"}
        ],
        readingType: [
          {required: true, message: "读数类型不能为空", trigger: "change"}
        ],
        clientAccess: [
          {required: true, message: "操作权限不能为空", trigger: "blur"}
        ],
        dataType: [
          {required: true, message: "数据类型不能为空", trigger: "change"}
        ],
        address: [
          {required: true, message: "地址不能为空", trigger: "blur"}
        ],
        value: [
          {required: false, message: "当前值不能为空", trigger: "blur"}
        ],
        valueType: [
          {required: true, message: "控制值类型不能为空", trigger: "change"}
        ],
        valueMax: [
          {required: false, message: "最大值不能为空", trigger: "blur"}
        ],
        valueMin: [
          {required: false, message: "最小值不能为空", trigger: "blur"}
        ],
        valueOption: [
          {required: false, message: "控制值选项不能为空", trigger: "blur"}
        ],
        remark: [
          {required: false, message: "备注不能为空", trigger: "blur"}
        ]
      }
    };
  },
  created() {
    this.getList();
    this.getDeviceOption();
  },
  methods: {
    // 绑定设备
    autoBoundDevice(){
      boundDevice().then(res => {
        this.$modal.msgSuccess("绑定成功");
      })
    },
    // 设备选项
    getDeviceOption() {
      deviceOption().then(res => {
        this.deviceOptions = res.data
      })
    },
    /** 查询设备点位列表 */
    getList() {
      this.loading = true;
      listDevicePoint(this.queryParams).then(response => {
        this.devicePointList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: undefined,
        name: undefined,
        tagName: undefined,
        deviceId: undefined,
        deviceName: undefined,
        fieldName: undefined,
        analyseFlag: "0",
        readingType: undefined,
        clientAccess: undefined,
        dataType: undefined,
        address: undefined,
        value: undefined,
        unit: undefined,
        valueType: "1",
        valueOption: "[]",
        valueMax: undefined,
        valueMin: undefined,
        remark: undefined,
        energyFlag: undefined,
        updateTime: undefined
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加设备点位";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.loading = true;
      this.reset();
      const id = row.id || this.ids
      getDevicePoint(id).then(response => {
        this.loading = false;
        this.form = response.data;
        // 确保valueOption是字符串类型
        if (this.form.valueOption && typeof this.form.valueOption !== 'string') {
          this.form.valueOption = JSON.stringify(this.form.valueOption);
        } else if (!this.form.valueOption) {
          this.form.valueOption = '[]';
        }
        this.open = true;
        this.title = "修改设备点位";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.buttonLoading = true;
          // 根据valueType处理表单数据
          const formData = {...this.form};

          // 如果不是数值类型，清空最大值和最小值
          if (formData.valueType !== '1') {
            formData.valueMax = null;
            formData.valueMin = null;
          }

          // 如果不是枚举类型，清空选项值
          if (formData.valueType !== '2') {
            formData.valueOption = null;
          }

          if (this.form.id != null) {
            updateDevicePoint(formData).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            }).finally(() => {
              this.buttonLoading = false;
            });
          } else {
            addDevicePoint(formData).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            }).finally(() => {
              this.buttonLoading = false;
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除？').then(() => {
        this.loading = true;
        return delDevicePoint(ids);
      }).then(() => {
        this.loading = false;
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {
      }).finally(() => {
        this.loading = false;
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('biz/devicePoint/export', {
        ...this.queryParams
      }, `devicePoint_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
