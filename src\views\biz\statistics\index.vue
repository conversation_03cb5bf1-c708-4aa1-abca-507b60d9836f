<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!--区域数据-->
      <el-col :span="4" :xs="24">
        <el-col style="margin-bottom: 10px">
          <el-radio v-for="dict in dict.type.energy_type" :key="dict.value"
                    :label="dict.value"
                    v-model="energyType"
                    @input="selectType"
          >{{ dict.label }}
          </el-radio>
        </el-col>
        <div class="head-container">
          <el-input
            v-model="areaName"
            placeholder="输入关键字以查找"
            clearable
            size="small"
            prefix-icon="el-icon-search"
            style="margin-bottom: 20px"
          />
        </div>
        <div class="head-container" v-loading="typeLoading">
          <el-tree
            :data="meterTreeOptions"
            :props="props"
            :expand-on-click-node="false"
            :filter-node-method="filterNode"
            ref="tree"
            node-key="id"
            show-checkbox
            default-expand-all
            highlight-current
          />
        </div>
      </el-col>
      <!--能耗数据-->
      <el-col :span="20" :xs="24">
        <time-analysis-selector
          ref="timeAnalysisSelector"
          @params-change="handleTimeParamsChange"
        >
          <template #front>
            <el-form-item label="点位类型" prop="pointType">
              <el-select
                v-model="queryParams.pointType"
                placeholder="请选择"
                clearable
                style="width: 100%"
              >
                <el-option
                  v-for="option in dict.type.point_type"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </template>
          <template #actions>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            <el-button
              type="warning"
              plain
              icon="el-icon-download"
              size="mini"
              @click="handleExport"
            >导出
            </el-button>
          </template>
        </time-analysis-selector>

        <!-- 表格数据 -->
        <div style="margin-top: 20px;">
          <el-table
            v-loading="loading"
            v-if="tableData && tableData.length > 0"
            :data="tableData"
            border
            :default-sort="{prop: 'totalEnergy', order: 'descending'}"
            style="width: 100%">
            <el-table-column
              align="center"
              prop="meterName"
              label="表具名称"
              fixed="left"
              width="150">
            </el-table-column>
            <el-table-column
              align="center"
              prop="meterNumber"
              label="表具编号"
              fixed="left"
              width="150">
            </el-table-column>
            <el-table-column
              align="center"
              prop="areaName"
              label="所属区域"
              fixed="left"
              width="180">
            </el-table-column>
            <el-table-column
              align="center"
              v-for="(value, date) in dateColumns"
              :key="date"
              :prop="'energyData.' + date"
              :label="date"
              sortable
              :sort-method="(a, b) => sortByDate(a, b, date)">
              <template slot-scope="scope">
                {{ formatEnergyValue(scope.row.energyData[date]) }}
              </template>
            </el-table-column>
            <el-table-column
              align="center"
              prop="totalEnergy"
              label="总计"
              sortable
              width="100">
              <template slot-scope="scope">
                {{ calculateTotal(scope.row.energyData) }}
              </template>
            </el-table-column>
          </el-table>
          <div v-else class="empty-block">
            <el-empty description="暂无数据"></el-empty>
          </div>

          <!-- 分页 -->
          <pagination
            v-if="total > 0"
            :total="total"
            :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize"
            @pagination="getList"
          />
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import TimeAnalysisSelector from '@/components/TimeAnalysisSelector/index.vue'
import {meterTree} from '@/api/biz/meter'
import {statisticsEnergy} from '@/api/biz/statistics'

export default {
  name: 'statistics',
  components: {
    TimeAnalysisSelector
  },
  dicts: ['energy_type', 'point_type'],
  data() {
    return {
      meterTreeOptions: [],
      props: {
        multiple: true, emitPath: false,
        value: 'id', // 绑定的值字段名
        label: 'name', // 显示的文字字段名
        children: 'children' // 子选项字段名
      },
      // 区域名称
      areaName: undefined,
      energyType: '1',

      typeLoading: false,

      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        meterIds: [],
        analysisType: undefined,
        pointType: undefined,
        startTime: undefined,
        endTime: undefined
      },
      // 表格数据
      tableData: [],
      // 日期列
      dateColumns: {},
      // 遮罩层
      loading: false,
      // 总条数
      total: 0,
    }
  },
  watch: {
    // 根据名称筛选部门树
    areaName(val) {
      this.$refs.tree.filter(val)
    }
  },
  created() {
    this.getMeterTree()
  },
  methods: {
    selectType(data) {
      this.getMeterTree()
    },
    // 获取区域表具树结构
    getMeterTree() {
      this.typeLoading = true
      meterTree({
        energyType: this.energyType
      }).then(res => {
        this.meterTreeOptions = res.data
        this.typeLoading = false
      })
    },
    /** 重置按钮操作 */
    resetQuery() {
      // 重置时间选择器
      this.$refs.timeAnalysisSelector.reset()
      // 重置其他参数
      this.queryParams.meterIds = []
      this.queryParams.pageNum = 1
      this.queryParams.pageSize = 10

      // 清除树选中状态 - 使用setCheckedKeys清空所有选中项
      this.$refs.tree.setCheckedKeys([])

      // 清空表格数据
      this.tableData = []
      this.total = 0
      this.dateColumns = {}
    },
    // 筛选节点
    filterNode(value, data) {
      if (!value) return true
      return data.name.indexOf(value) !== -1
    },
    /** 搜索按钮操作 */
    handleQuery() {
      if (!this.verify()) {
        return
      }
      this.queryParams.pageNum = 1
      this.getList()
    },
    verify() {
      // 检查必要参数
      if (!this.queryParams.analysisType || !this.queryParams.startTime ||
        !this.queryParams.endTime || !this.queryParams.pointType) {
        this.$message.warning('请选择表具、点位、分析方式和时间范围');
        return false;
      }

      // 获取选中的节点
      const checkedNodes = this.$refs.tree.getCheckedNodes();

      // 筛选出类型为 "meter" 的节点 ID
      this.queryParams.meterIds = checkedNodes
        .filter(node => node.type === 'meter')
        .map(node => node.id);

      // 检查是否选择了表具
      if (this.queryParams.meterIds.length === 0) {
        this.$message.warning('请至少选择一个表具');
        return false;
      }
      return true;
    },
    getList() {
      this.loading = true
      statisticsEnergy(this.queryParams).then(res => {
        // 确保tableData始终是一个数组
        this.tableData = res.rows || []
        this.total = res.total || 0

        // 提取所有日期列
        if (this.tableData.length > 0) {
          // 从第一条数据中获取日期列
          const firstRow = this.tableData[0]
          this.dateColumns = firstRow.energyData || {}

          // 计算并添加总能耗字段用于排序
          this.tableData.forEach(item => {
            item.totalEnergy = this.calculateTotalNumber(item.energyData)
          })
        } else {
          this.dateColumns = {}
        }

        this.loading = false
      }).catch(() => {
        this.loading = false
        this.tableData = []
        this.total = 0
        this.dateColumns = {}
      })
    },
    // 处理时间参数变更
    handleTimeParamsChange(params) {
      this.queryParams.analysisType = params.analysisType
      this.queryParams.startTime = params.startTime
      this.queryParams.endTime = params.endTime
    },

    // 格式化能耗值
    formatEnergyValue(value) {
      if (!value || value === '0') return '0'
      // 转换为数字并保留2位小数
      return parseFloat(value).toFixed(2)
    },

    // 计算总能耗（返回数字）
    calculateTotalNumber(energyData) {
      if (!energyData) return 0

      let total = 0
      for (const date in energyData) {
        const value = energyData[date]
        if (value && value !== '0') {
          total += parseFloat(value)
        }
      }

      return total
    },

    // 计算总能耗（返回格式化字符串）
    calculateTotal(energyData) {
      return this.calculateTotalNumber(energyData).toFixed(2)
    },

    // 按日期排序
    sortByDate(a, b, date) {
      const valueA = a && a.energyData && a.energyData[date] ? parseFloat(a.energyData[date]) : 0
      const valueB = b && b.energyData && b.energyData[date] ? parseFloat(b.energyData[date]) : 0
      return valueA - valueB
    },
    /** 导出按钮操作 */
    handleExport() {
      if (!this.verify()) {
        return
      }
      this.download('biz/statistics/export', {
        ...this.queryParams
      }, `能耗统计_${new Date().getTime()}.xlsx`)
    }
  }
}
</script>

<style scoped lang="scss">
::v-deep .el-radio__original {
  display: none !important;
}

::v-deep .el-radio:focus:not(.is-focus):not(:active):not(.is-disabled)
.el-radio__inner {
  box-shadow: none !important;
}

.empty-block {
  text-align: center;
  padding: 30px 0;
  color: #909399;
  font-size: 14px;
}
</style>
